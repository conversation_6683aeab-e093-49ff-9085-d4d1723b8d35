# -*- coding: utf-8 -*-
"""Resume Job recommendation system.ipynb

Automatically generated by <PERSON>b.

Original file is located at
    https://colab.research.google.com/drive/1nzh1kYPjdsnR86RgyFe3igBZzfziYjdD
"""

import pandas as pd

df = pd.read_csv('/content/jobs_dataset_with_features.csv')

df.head()

df.shape

df['Role'].value_counts()

# Dropping classes with less than 6500 instances
min_count = 6500
role_count = df['Role'].value_counts()
to_drop = role_count[role_count < min_count].index
filtered_df = df[~df['Role'].isin(to_drop)].reset_index(drop=True)
filtered_df['Role'].value_counts()

len(filtered_df['Role'].value_counts())

df = filtered_df.sample(n=10000)

df.head()

from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score
from sklearn.model_selection import train_test_split
from sklearn.feature_extraction.text import TfidfVectorizer


# Splitting the data into features (X) and target (y)
X = df['Features']
y = df['Role']

# Train-test split
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# TF-IDF vectorization
tfidf_vectorizer = TfidfVectorizer()
X_train_tfidf = tfidf_vectorizer.fit_transform(X_train)
X_test_tfidf = tfidf_vectorizer.transform(X_test)

# RandomForestClassifier
rf_classifier = RandomForestClassifier()
rf_classifier.fit(X_train_tfidf, y_train)

# Predictions
y_pred = rf_classifier.predict(X_test_tfidf)

# Accuracy
accuracy = accuracy_score(y_test, y_pred)
print("Accuracy:", accuracy)

# Clean resume
import re

def cleanResume(txt):
    cleanText = re.sub('http\S+\s*', ' ', txt)
    cleanText = re.sub('RT|cc', ' ', cleanText)
    cleanText = re.sub('@\S+', ' ', cleanText)
    cleanText = re.sub('#\S+', ' ', cleanText)
    cleanText = re.sub('[%s]' % re.escape("""!"#$%&'()*+,-./:;<=>?@[\]^_`{|}~"""), ' ', cleanText)
    cleanText = re.sub(r'[^\x00-\x7f]', ' ', cleanText)
    cleanText = re.sub('\s+', ' ', cleanText)
    return cleanText

# Prediction and Category Name
def job_recommendation(resume_text):
    resume_text = cleanResume(resume_text)
    resume_tfidf = tfidf_vectorizer.transform([resume_text])
    predicted_category = rf_classifier.predict(resume_tfidf)[0]
    return predicted_category

resume_file = """
Tayyeb Ullah
📧 Email: <EMAIL>
📞 Phone: 0317-9818016
🌍 Languages: Urdu, English, Pashto

🎯 Professional Summary
Passionate and results-driven Machine Learning Engineer with over 1 year of experience in building, training, and deploying AI models using Python, ML/DL frameworks, and natural language processing. Strong background in software engineering with a focus on practical and scalable AI solutions.

💻 Technical Skills
Programming Languages: Python

Machine Learning (ML): Supervised/Unsupervised Learning, Model Evaluation

Deep Learning (DL): Neural Networks, CNN, RNN

Natural Language Processing (NLP): Text classification, Named Entity Recognition

Large Language Models (LLMs): LangChain, GPT, LLaMA, etc.

Tools & Libraries: Scikit-learn, TensorFlow, PyTorch, NLTK, HuggingFace

Version Control: Git & GitHub

💼 Professional Experience
Machine Learning Engineer
AI Research Lab (Remote / Freelance)
📅 Jan 2023 – Present

Developed and optimized NLP pipelines for document classification and information retrieval.

Implemented transformer-based models and fine-tuned LLMs for custom use cases.

Built end-to-end ML workflows and integrated with Streamlit apps.

Collaborated in a team to deploy and evaluate models using real-world datasets.

🎓 Education
Bachelor of Science in Software Engineering
UET Mardan
📅 Graduated: 2024

🗣️ Languages
Urdu (Native)

English (Fluent)

Pashto (Native)

🚀 Projects & Highlights
Resume Recommendation System: Built a resume classifier using TF-IDF and RandomForest to recommend job categories.

RAG Chatbot: Developed a chatbot using LangChain, FAISS, and Groq LLMs for Q&A on PDF documents.

Sentiment Analysis: Deployed a sentiment analyzer using IMDB dataset with real-time prediction via Streamlit.

"""
predicted_category = job_recommendation(resume_file)
print("predicated_category:",predicted_category)

import pickle
pickle.dump(rf_classifier, open('model.pkl', 'wb'))
pickle.dump(tfidf_vectorizer, open('tfidf.pkl', 'wb'))

