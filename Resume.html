<!-- <!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Resume Screening AI</title>
  <style>
    /* Global styles */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Arial', sans-serif;
      background: linear-gradient(135deg, #667eea, #764ba2);
      min-height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px;
    }

    .container {
      background: #fff;
      border-radius: 15px;
      padding: 30px;
      max-width: 700px;
      width: 100%;
      box-shadow: 0 0 20px rgba(0,0,0,0.1);
      text-align: center;
    }

    .header h1 {
      font-size: 2rem;
      color: #2c3e50;
      margin-bottom: 10px;
    }

    .header p {
      font-size: 1rem;
      color: #555;
      margin-bottom: 20px;
    }

    .feature-item {
      display: inline-block;
      background: #2980b9;
      color: white;
      padding: 8px 15px;
      margin: 5px;
      border-radius: 20px;
      font-size: 0.9rem;
    }

    .upload-section {
      margin: 30px 0;
      border: 2px dashed #667eea;
      border-radius: 10px;
      padding: 30px 20px;
    }

    .file-input-label {
      display: inline-block;
      background: #667eea;
      color: white;
      padding: 10px 25px;
      border-radius: 25px;
      cursor: pointer;
      margin-bottom: 15px;
    }

    #fileInput {
      display: none;
    }

    .text-input {
      width: 100%;
      height: 120px;
      padding: 10px;
      margin-top: 10px;
      border-radius: 10px;
      border: 1px solid #ccc;
      resize: vertical;
    }

    .analyze-btn {
      background: #27ae60;
      color: white;
      padding: 12px 30px;
      margin-top: 20px;
      border: none;
      border-radius: 25px;
      cursor: pointer;
      font-weight: bold;
    }

    .results {
      margin-top: 30px;
      text-align: left;
    }

    .job-item, .skill-tag {
      background: #3498db;
      color: white;
      padding: 10px;
      margin: 5px;
      display: inline-block;
      border-radius: 8px;
    }

    .category {
      font-weight: bold;
      font-size: 1.2rem;
      margin-bottom: 10px;
    }

    .confidence {
      background: #f39c12;
      color: white;
      padding: 8px 15px;
      display: inline-block;
      margin-bottom: 15px;
      border-radius: 15px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🎯 Resume Screening AI</h1>
      <p>AI-Powered Career Analysis & Job Recommendations</p>
      <div>
        <div class="feature-item">📊 Resume Categorization</div>
        <div class="feature-item">💼 Job Recommendations</div>
        <div class="feature-item">🔍 Skill Analysis</div>
      </div>
    </div>

    <div class="upload-section">
      <label for="fileInput" class="file-input-label">📁 Choose Resume File</label>
      <input type="file" id="fileInput" accept=".txt" />
      <p>OR Paste Resume Below</p>
      <textarea id="textInput" class="text-input" placeholder="Paste resume text here..."></textarea>
      <button class="analyze-btn" onclick="analyzeResume()">🚀 Analyze Resume</button>
    </div>

    <div id="results" class="results" style="display: none;">
      <div class="category" id="jobCategory"></div>
      <div class="confidence" id="confidenceScore"></div>
      <h3>💼 Recommended Jobs:</h3>
      <div id="jobList"></div>
      <h3>🛠️ Key Skills:</h3>
      <div id="skillsList"></div>
    </div>
  </div>

  <script>
    // Define categories
    const jobCategories = {
      "HR": {
        jobs: ["HR Manager", "Talent Acquisition", "Training Specialist", "People Partner"],
        skills: ["Communication", "Leadership", "HRIS", "Recruiting", "Performance Management"]
      },
      "Web Design": {
        jobs: ["UI/UX Designer", "Web Designer", "Frontend Developer"],
        skills: ["HTML", "CSS", "JavaScript", "Figma", "Adobe XD"]
      },
      "Data Science": {
        jobs: ["Data Scientist", "ML Engineer", "Data Analyst"],
        skills: ["Python", "SQL", "Pandas", "Scikit-learn", "Visualization"]
      }
    };

    // Load file content to textarea
    document.getElementById('fileInput').addEventListener('change', function(e) {
      const file = e.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = function(e) {
        document.getElementById('textInput').value = e.target.result;
      };
      reader.readAsText(file);
    });

    // Resume analysis (mock)
    function analyzeResume() {
      const text = document.getElementById('textInput').value.trim();
      if (!text) {
        alert("⚠️ Please upload a file or enter resume text.");
        return;
      }

      const categories = Object.keys(jobCategories);
      const predicted = categories[Math.floor(Math.random() * categories.length)];
      const confidence = Math.floor(Math.random() * 30) + 70;

      // Display result
      const categoryData = jobCategories[predicted];
      document.getElementById('jobCategory').textContent = `Category: ${predicted}`;
      document.getElementById('confidenceScore').textContent = `Confidence: ${confidence}%`;

      const jobList = document.getElementById('jobList');
      jobList.innerHTML = "";
      categoryData.jobs.forEach(job => {
        const div = document.createElement('div');
        div.className = "job-item";
        div.textContent = job;
        jobList.appendChild(div);
      });

      const skillList = document.getElementById('skillsList');
      skillList.innerHTML = "";
      categoryData.skills.forEach(skill => {
        const div = document.createElement('div');
        div.className = "skill-tag";
        div.textContent = skill;
        skillList.appendChild(div);
      });

      document.getElementById('results').style.display = "block";
    }
  </script>
</body>
</html> -->




<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Resume Screening AI</title>
  <style>
    /* Global styles */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Arial', sans-serif;
      background: linear-gradient(135deg, #667eea, #764ba2);
      min-height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px;
    }

    .container {
      background: #fff;
      border-radius: 15px;
      padding: 30px;
      max-width: 700px;
      width: 100%;
      box-shadow: 0 0 20px rgba(0,0,0,0.1);
      text-align: center;
    }

    .header h1 {
      font-size: 2rem;
      color: #2c3e50;
      margin-bottom: 10px;
    }

    .header p {
      font-size: 1rem;
      color: #555;
      margin-bottom: 20px;
    }

    .feature-item {
      display: inline-block;
      background: #2980b9;
      color: white;
      padding: 8px 15px;
      margin: 5px;
      border-radius: 20px;
      font-size: 0.9rem;
    }

    .feature-item.active {
      background: #27ae60;
    }

    .upload-section {
      margin: 30px 0;
      border: 2px dashed #667eea;
      border-radius: 10px;
      padding: 30px 20px;
    }

    .file-input-label {
      display: inline-block;
      background: #667eea;
      color: white;
      padding: 10px 25px;
      border-radius: 25px;
      cursor: pointer;
      margin-bottom: 15px;
    }

    #fileInput {
      display: none;
    }

    .text-input {
      width: 100%;
      height: 120px;
      padding: 10px;
      margin-top: 10px;
      border-radius: 10px;
      border: 1px solid #ccc;
      resize: vertical;
    }

    .analyze-btn {
      background: #27ae60;
      color: white;
      padding: 12px 30px;
      margin-top: 20px;
      border: none;
      border-radius: 25px;
      cursor: pointer;
      font-weight: bold;
      transition: all 0.3s;
    }

    .analyze-btn:hover {
      background: #219653;
      transform: translateY(-2px);
    }

    .results {
      margin-top: 30px;
      text-align: left;
    }

    .job-item, .skill-tag {
      background: #3498db;
      color: white;
      padding: 8px 15px;
      margin: 5px;
      display: inline-block;
      border-radius: 20px;
      font-size: 0.9rem;
    }

    .category {
      font-weight: bold;
      font-size: 1.2rem;
      margin-bottom: 10px;
    }

    .confidence {
      background: #f39c12;
      color: white;
      padding: 8px 15px;
      display: inline-block;
      margin-bottom: 15px;
      border-radius: 15px;
      font-size: 0.9rem;
    }

    .progress-container {
      width: 100%;
      background-color: #e0e0e0;
      border-radius: 10px;
      margin: 10px 0;
    }

    .progress-bar {
      height: 20px;
      border-radius: 10px;
      background-color: #4CAF50;
      text-align: center;
      line-height: 20px;
      color: white;
      font-size: 12px;
    }

    .loading {
      display: none;
      margin: 20px 0;
    }

    .spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      border-top: 4px solid #3498db;
      width: 30px;
      height: 30px;
      animation: spin 1s linear infinite;
      margin: 0 auto;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🎯 Resume Screening AI</h1>
      <p>AI-Powered Career Analysis & Job Recommendations</p>
      <div>
        <div class="feature-item active">📊 Resume Categorization</div>
        <div class="feature-item active">💼 Job Recommendations</div>
        <div class="feature-item">🔍 Skill Analysis</div>
      </div>
    </div>

    <div class="upload-section">
      <label for="fileInput" class="file-input-label">📁 Choose Resume File</label>
      <input type="file" id="fileInput" accept=".txt,.pdf,.docx" />
      <p>OR Paste Resume Below</p>
      <textarea id="textInput" class="text-input" placeholder="Paste resume text here..."></textarea>
      <button class="analyze-btn" onclick="analyzeResume()">🚀 Analyze Resume</button>
      <div class="loading" id="loadingIndicator">
        <div class="spinner"></div>
        <p>Analyzing resume...</p>
      </div>
    </div>

    <div id="results" class="results" style="display: none;">
      <div class="category" id="jobCategory"></div>
      <div class="confidence" id="confidenceScore"></div>
      <div class="progress-container">
        <div class="progress-bar" id="confidenceBar"></div>
      </div>
      <h3>💼 Recommended Jobs:</h3>
      <div id="jobList"></div>
    </div>
  </div>

  <script>
    // Load file content to textarea
    document.getElementById('fileInput').addEventListener('change', function(e) {
      const file = e.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = function(e) {
        document.getElementById('textInput').value = e.target.result;
      };
      reader.readAsText(file);
    });

    // Resume analysis function
    async function analyzeResume() {
      const text = document.getElementById('textInput').value.trim();
      if (!text) {
        alert("⚠️ Please upload a file or enter resume text.");
        return;
      }

      // Show loading indicator
      document.getElementById('loadingIndicator').style.display = 'block';
      document.getElementById('results').style.display = 'none';

      try {
        // In a real implementation, you would send this to your backend
        // For this example, we'll simulate the API call with a timeout
        const result = await simulateApiCall(text);
        
        // Display result
        document.getElementById('jobCategory').textContent = `Category: ${result.category}`;
        document.getElementById('confidenceScore').textContent = `Confidence: ${result.confidence}%`;
        document.getElementById('confidenceBar').style.width = `${result.confidence}%`;
        document.getElementById('confidenceBar').textContent = `${result.confidence}%`;

        const jobList = document.getElementById('jobList');
        jobList.innerHTML = "";
        result.recommendedJobs.forEach(job => {
          const div = document.createElement('div');
          div.className = "job-item";
          div.textContent = job;
          jobList.appendChild(div);
        });

        document.getElementById('results').style.display = "block";
      } catch (error) {
        alert("Error analyzing resume: " + error.message);
      } finally {
        document.getElementById('loadingIndicator').style.display = 'none';
      }
    }

    // Simulate API call to your Python backend
    function simulateApiCall(resumeText) {
      return new Promise((resolve) => {
        setTimeout(() => {
          // This is where you would normally make a fetch() call to your backend
          // For this example, we'll simulate the response based on your Python code
          
          // Sample categories from your Python files
          const possibleCategories = [
            "Data Science", "Software Engineering", "Web Development", 
            "Machine Learning", "HR", "Education", "Business Analysis"
          ];
          
          // Random selection for demo (in real app, this would come from your model)
          const randomCategory = possibleCategories[Math.floor(Math.random() * possibleCategories.length)];
          const confidence = Math.floor(Math.random() * 30) + 70; // 70-100%
          
          // Sample job recommendations based on category
          let recommendedJobs = [];
          if (randomCategory === "Data Science") {
            recommendedJobs = ["Data Scientist", "ML Engineer", "Data Analyst"];
          } else if (randomCategory === "Software Engineering") {
            recommendedJobs = ["Software Engineer", "Backend Developer", "Full Stack Developer"];
          } else if (randomCategory === "Web Development") {
            recommendedJobs = ["Frontend Developer", "UI/UX Designer", "Web Developer"];
          } else if (randomCategory === "Machine Learning") {
            recommendedJobs = ["Machine Learning Engineer", "AI Specialist", "Deep Learning Engineer"];
          } else if (randomCategory === "HR") {
            recommendedJobs = ["HR Manager", "Recruiter", "Talent Acquisition Specialist"];
          } else {
            recommendedJobs = [`${randomCategory} Professional`, `${randomCategory} Specialist`, `${randomCategory} Consultant`];
          }

          resolve({
            category: randomCategory,
            confidence: confidence,
            recommendedJobs: recommendedJobs
          });
        }, 1500); // Simulate network delay
      });
    }
  </script>
</body>
</html>